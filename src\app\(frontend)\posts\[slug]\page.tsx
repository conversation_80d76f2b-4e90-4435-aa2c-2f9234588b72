import type { Metada<PERSON> } from "next";
import { client, sanityFetch } from "@/sanity/lib/client";
// import the client and sanityFetch function
import { Post } from "@/components/Post";
import { notFound } from "next/navigation";
// update your imports
import { POST_QUERY, POSTS_SLUGS_QUERY } from "@/sanity/lib/queries";
import { urlFor } from "@/sanity/lib/image";

type RouteProps = {
  params: Promise<{ slug: string }>;
};

// add this export
export async function generateStaticParams() {
  // False when responses are infrequent and fast responses are not required.
  const slugs = await client
    .withConfig({ useCdn: false })
    .fetch(POSTS_SLUGS_QUERY);

  return slugs;
}

const getPost = async (params: RouteProps["params"]) => {
  const { slug } = await params;

  return sanityFetch({
    query: POST_QUERY,
    params: { slug },
    tags: [`post:${slug}`, "author", "category"],
  });
};

export async function generateMetadata({
  params,
}: RouteProps): Promise<Metadata> {
  const { data: post } = await getPost(params);

  if (!post) {
    return {};
  }

  const metadata: Metadata = {
    title: post.seo.title,
    description: post.seo.description,
  };

  if (post.seo.image) {
    metadata.openGraph = {
      images: {
        url: urlFor(post.seo.image).width(1200).height(630).url(),
        width: 1200,
        height: 630,
      },
    };
  }

  if (post.seo.noIndex) {
    metadata.robots = "noindex";
  }

  return metadata;
}

export default async function Page({ params }: RouteProps) {
  const { data: post } = await getPost(params);

  if (!post) {
    notFound();
  }

  return (
    <main className="container mx-auto grid grid-cols-1 gap-6 p-12">
      <Post {...post} />
    </main>
  );
}
