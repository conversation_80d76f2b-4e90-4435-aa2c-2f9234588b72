import type { Metadata } from "next";
import { client, sanityFetch } from "@/sanity/lib/client";
// import the client and sanityFetch function
import { Post } from "@/components/Post";
import { notFound } from "next/navigation";
// update your imports
import { POST_QUERY, POSTS_SLUGS_QUERY } from "@/sanity/lib/queries";

type RouteProps = {
  params: Promise<{ slug: string }>;
};

// add this export
export async function generateStaticParams() {
  // False when responses are infrequent and fast responses are not required.
  const slugs = await client
    .withConfig({ useCdn: false })
    .fetch(POSTS_SLUGS_QUERY);

  return slugs;
}

const getPost = async (params: RouteProps["params"]) => {
  const { slug } = await params;

  return sanityFetch({
    query: POST_QUERY,
    params: { slug },
    tags: [`post:${slug}`, "author", "category"],
  });
};

export async function generateMetadata({
  params,
}: RouteProps): Promise<Metadata> {
  const { data: post } = await getPost(params);

  return {
    title: post?.seo?.title || post?.title,
  };
}

export default async function Page({ params }: RouteProps) {
  const { data: post } = await getPost(params);

  if (!post) {
    notFound();
  }

  return (
    <main className="container mx-auto grid grid-cols-1 gap-6 p-12">
      <Post {...post} />
    </main>
  );
}
